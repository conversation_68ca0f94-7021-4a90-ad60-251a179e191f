import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import '../../../../core/resources/resources.dart';

class PinCodeFields extends StatefulWidget {
  const PinCodeFields({
    Key? key,
    required this.pinController,
    required this.onCompleted,
  }) : super(key: key);
  final TextEditingController pinController;
  final void Function(String)? onCompleted;

  @override
  State<PinCodeFields> createState() => _PinCodeFieldsState();
}

class _PinCodeFieldsState extends State<PinCodeFields> {
  final focusNode = FocusNode();
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 57,
      height: 73,
      textStyle:
          context.typography.large2xSemi.copyWith(color: AppColors.white),
      decoration: BoxDecoration(
        color: AppColors.primaryMedium,
        borderRadius: BorderRadius.circular(4),
        border: const Border(
            bottom: BorderSide(width: 1.5, color: AppColors.primaryXLight)),
      ),
    );
    return Form(
      key: formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Pinput(
            length: 6,
            controller: widget.pinController,
            focusNode: focusNode,
            defaultPinTheme: defaultPinTheme,
            separatorBuilder: (index) => const SizedBox(width: 8),
            hapticFeedbackType: HapticFeedbackType.lightImpact,
            onCompleted: widget.onCompleted,
            onChanged: (value) {
              debugPrint('onChanged: $value');
            },
            cursor: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  margin: const EdgeInsets.only(bottom: 9),
                  width: 28,
                  height: 2,
                  color: AppColors.primaryLight,
                ),
              ],
            ),
            obscureText: true,
            obscuringCharacter: '#',
            focusedPinTheme: defaultPinTheme,
            submittedPinTheme: defaultPinTheme,
            errorPinTheme: defaultPinTheme,
          ),
        ],
      ),
    );
  }
}
