import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:flutter/material.dart';

class AppCheckboxTile<T> extends StatelessWidget {
  const AppCheckboxTile({
    super.key,
    required this.value,
    required this.label,
    required this.onChanged,
  });

  final bool value;
  final String label;
  final void Function(bool?) onChanged;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            onChanged(!value);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: const VisualDensity(
                  horizontal: VisualDensity.minimumDensity,
                  vertical: -1.5,
                ),
                value: value,
                onChanged: onChanged,
              ),
              spacerW8,
              Text(
                label,
                style: context.typography.mediumReg,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
