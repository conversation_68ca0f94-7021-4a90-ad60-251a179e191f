import 'package:json_annotation/json_annotation.dart';

part 'auth_response.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class User {
  User({
    required this.id,
    required this.email,
    this.appleIdentifier,
    this.confirmedAt,
    required this.favouriteBuyers,
    required this.hashedPassword,
    required this.insertedAt,
    this.password,
    required this.profile,
    required this.updatedAt,
    required this.token,
  });

  final int id;
  final String email;
  final String? appleIdentifier;
  final String? confirmedAt;
  final List<int> favouriteBuyers;
  final String? hashedPassword;
  final String? insertedAt;
  final String? password;
  final UserProfile? profile;
  final String? updatedAt;
  final String? token;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? email,
    String? appleIdentifier,
    String? confirmedAt,
    List<int>? favouriteBuyers,
    String? hashedPassword,
    String? insertedAt,
    String? password,
    UserProfile? profile,
    String? updatedAt,
    String? token,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      appleIdentifier: appleIdentifier ?? this.appleIdentifier,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      favouriteBuyers: favouriteBuyers ?? this.favouriteBuyers,
      hashedPassword: hashedPassword ?? this.hashedPassword,
      insertedAt: insertedAt ?? this.insertedAt,
      password: password ?? this.password,
      profile: profile ?? this.profile,
      updatedAt: updatedAt ?? this.updatedAt,
      token: token ?? this.token,
    );
  }
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class UserProfile {
  UserProfile({
    required this.id,
    required this.agentEmail,
    this.imageUrl,
    this.firstName,
    this.lastName,
    this.primaryPhoneNumber,
    this.brokerageName,
    this.brokerageLisenceNo,
    this.brokerageStreetAddress,
    this.brokerageCity,
    this.brokerageZipCode,
    this.brokerageState,
    this.isProfileCompleted = false,
    this.agentLicenseIdNo,
  });

  final int id;
  final String? agentEmail;
  final String? imageUrl;
  final String? firstName;
  final String? lastName;
  @JsonKey(name: "phone_number_primary")
  final String? primaryPhoneNumber;
  final String? brokerageName;
  final String? brokerageLisenceNo;
  @JsonKey(name: "broker_street_address")
  final String? brokerageStreetAddress;
  @JsonKey(name: "broker_city")
  final String? brokerageCity;
  final String? brokerageZipCode;
  final String? brokerageState;
  @JsonKey(name: "is_completed", defaultValue: false)
  final bool isProfileCompleted;
  @JsonKey(name: 'lisence_id_no')
  final String? agentLicenseIdNo;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    int? id,
    String? agentEmail,
    String? imageUrl,
    String? firstName,
    String? lastName,
    String? primaryPhoneNumber,
    String? brokerageName,
    String? brokerageLisenceNo,
    String? brokerageStreetAddress,
    String? brokerageCity,
    String? brokerageZipCode,
    String? brokerageState,
    bool? isProfileCompleted,
    String? agentLicenseIdNo,
  }) {
    return UserProfile(
      id: id ?? this.id,
      agentEmail: agentEmail ?? this.agentEmail,
      imageUrl: imageUrl ?? this.imageUrl,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      primaryPhoneNumber: primaryPhoneNumber ?? this.primaryPhoneNumber,
      brokerageName: brokerageName ?? this.brokerageName,
      brokerageLisenceNo: brokerageLisenceNo ?? this.brokerageLisenceNo,
      brokerageStreetAddress:
          brokerageStreetAddress ?? this.brokerageStreetAddress,
      brokerageCity: brokerageCity ?? this.brokerageCity,
      brokerageZipCode: brokerageZipCode ?? this.brokerageZipCode,
      brokerageState: brokerageState ?? this.brokerageState,
      isProfileCompleted: isProfileCompleted ?? this.isProfileCompleted,
      agentLicenseIdNo: agentLicenseIdNo ?? this.agentLicenseIdNo,
    );
  }
}
