import 'dart:io';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/add_buyer_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/resources/resources.dart';

class CircularImageWidget extends StatefulWidget {
  const CircularImageWidget(
      {super.key,
      this.buyerProfileImage,
      this.imageHeight = 200,
      this.imageWidth = 200,
      this.borderRadius,
      this.backGroundColor});

  final String? buyerProfileImage;
  final double imageHeight;
  final double imageWidth;
  final double? borderRadius;
  final Color? backGroundColor;

  @override
  State<CircularImageWidget> createState() => _CircularImageWidgetState();
}

class _CircularImageWidgetState extends State<CircularImageWidget> {
  String? galleryImage;
  Future<void> selectProfileImage() async {
    galleryImage = await context.read<AddBuyerCubit>().selectProfileImage();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.borderRadius ?? 100),
      child: InkWell(
        borderRadius: BorderRadius.circular(widget.borderRadius ?? 100),
        onTap: () async {
          await selectProfileImage();
        },
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Container(
              width: widget.imageWidth,
              height: widget.imageHeight,
              decoration: BoxDecoration(
                color: widget.backGroundColor ?? colorScheme.primary,
                borderRadius: BorderRadius.circular(widget.borderRadius ?? 100),
              ),
              child: galleryImage != null
                  ? Image.file(
                      File(galleryImage!),
                      fit: BoxFit.cover,
                    )
                  : widget.buyerProfileImage != null
                      ? Image.network(
                          widget.buyerProfileImage!,
                          fit: BoxFit.cover,
                        )
                      : Image.asset(
                          Drawables.person,
                          color: context.colorScheme.onPrimary,
                        ),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              height: 40,
              width: widget.imageWidth,
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(widget.borderRadius ?? 100),
                  bottomRight: Radius.circular(widget.borderRadius ?? 100),
                ),
              ),
              child: Text(
                galleryImage != null || widget.buyerProfileImage != null
                    ? Strings.edit
                    : Strings.add,
                textAlign: TextAlign.center,
                style: AppStyles.large.copyWith(
                  color: AppColors.white,
                  height: 0.8,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
