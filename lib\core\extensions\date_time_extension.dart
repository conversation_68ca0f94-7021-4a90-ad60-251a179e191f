import 'package:intl/intl.dart';

extension DateTimeExtensions on DateTime {
  // String getRelativeAge() {
  //   DateTime now = DateTime.now();

  //   // Check if the dates are the same day
  //   if (year == now.year && month == now.month && day == now.day) {
  //     return 'Today';
  //   }

  //   Duration difference = now.difference(this);

  //   // If the year and month are the same, display days ago
  //   if (year == now.year && month == now.month) {
  //     return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
  //   }

  //   // If the year is the same, display months ago
  //   if (year == now.year) {
  //     int months = (difference.inDays / 30).floor();
  //     return '$months ${months == 1 ? 'month' : 'months'} ago';
  //   }

  //   // Otherwise, display years ago
  //   int years = (difference.inDays / 365).floor();
  //   return '$years ${years == 1 ? 'year' : 'years'} ago';
  // }

  String formatRelativeDateTime() {
    final localTime = toLocal();
    final now = DateTime.now();
    final difference = now.difference(localTime);

    return switch (difference.inDays) {
      // Case for same day
      0 => DateFormat('h:mm a').format(localTime),

      // Case for days (singular and plural)
      < 7 =>
        difference.inDays == 1 ? '1 day ago' : '${difference.inDays} days ago',

      // Case for weeks (singular and plural)
      < 30 => (difference.inDays / 7).round() == 1
          ? '1 week ago'
          : '${(difference.inDays / 7).round()} weeks ago',

      // Case for months (singular and plural)
      < 365 => (difference.inDays / 30).round() == 1
          ? '1 month ago'
          : '${(difference.inDays / 30).round()} months ago',

      // Case for years (singular and plural)
      _ => (difference.inDays / 365).round() == 1
          ? '1 year ago'
          : '${(difference.inDays / 365).round()} years ago',
    };
  }
}
