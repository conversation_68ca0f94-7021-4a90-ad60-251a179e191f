import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/theme/app_color_extension.dart';
import 'package:buyer_board/core/theme/app_color_scheme.dart';
import 'package:buyer_board/core/theme/app_custom_color_scheme.dart';
import 'package:buyer_board/core/theme/app_typography.dart';
import 'package:buyer_board/core/theme/app_typography_extension.dart';
import 'package:flutter/material.dart';

extension AppThemeExtension on ThemeData {
  /// Usage example: Theme.of(context).appColors;
  AppColorsExtension get appColors =>
      extension<AppColorsExtension>() ?? AppCustomColorScheme.light;

  /// Usage example: Theme.of(context).appTypography;
  AppTypographyExtension get appTypography =>
      extension<AppTypographyExtension>() ??
      AppTypography.styles(AppCustomColorScheme.light.blackWhite);
}

abstract final class AppTheme {
  static ThemeData get light => ThemeData.light().copyWith(
        extensions: [
          AppCustomColorScheme.light,
          AppTypography.styles(AppCustomColorScheme.light.blackWhite)
        ],
        scaffoldBackgroundColor: AppColorScheme.light.surface,
        colorScheme: AppColorScheme.light,
        outlinedButtonTheme:
            _outlinedButtonTheme(primaryColor: AppColorScheme.light.primary),
        textButtonTheme:
            _textButtonTheme(primaryColor: AppColorScheme.light.primary),
        filledButtonTheme: _filledButtonThemeData(
          primaryColor: AppColorScheme.light.primary,
          onPrimaryColor: AppColors.white,
        ),
        expansionTileTheme: _expansionTileThemeData(
          backgroundColor: Colors.transparent,
          iconColor: AppColorScheme.light.primary,
          collapsedBorderColor: AppColorScheme.light.outlineVariant,
          textColor: AppColorScheme.light.primary,
        ),
        bottomNavigationBarTheme: _bottomNavigationBarThemeData(
          AppColorScheme.light.primary,
          AppColorScheme.light.onPrimary,
        ),
        dividerTheme: DividerThemeData(
          color: AppColorScheme.light.outlineVariant,
          thickness: 1,
        ),
        appBarTheme: _appThemeData(
            AppCustomColorScheme.light.whitePXLight, AppColors.primary),
        switchTheme: _switchThemeData(
          AppColors.white,
          AppColorScheme.light.primary,
        ),
        dialogTheme: _dialogTheme(
          backgroundColor: AppColorScheme.light.primary,
        ),
      );

  static ThemeData get dark => ThemeData.dark().copyWith(
        extensions: [
          AppCustomColorScheme.dark,
          AppTypography.styles(AppCustomColorScheme.dark.blackWhite)
        ],
        scaffoldBackgroundColor: AppColors.greyXDark,
        colorScheme: AppColorScheme.dark,
        bottomNavigationBarTheme: _bottomNavigationBarThemeData(
          AppColors.primaryDark,
          AppColors.white,
        ),
        expansionTileTheme: _expansionTileThemeData(
          backgroundColor: Colors.transparent,
          iconColor: AppColorScheme.dark.primary,
          collapsedBorderColor: AppColorScheme.dark.outlineVariant,
          textColor: AppColorScheme.dark.primary,
        ),
        dividerTheme: DividerThemeData(
          color: AppColorScheme.dark.outlineVariant,
          thickness: 1,
        ),
        outlinedButtonTheme:
            _outlinedButtonTheme(primaryColor: AppColorScheme.dark.primary),
        filledButtonTheme: _filledButtonThemeData(
          primaryColor: AppColorScheme.dark.primary,
          onPrimaryColor: AppColorScheme.dark.onPrimary,
        ),
        textButtonTheme:
            _textButtonTheme(primaryColor: AppColorScheme.dark.primary),
        appBarTheme: _appThemeData(
          AppCustomColorScheme.dark.whitePXLight,
          AppColors.primaryDark,
        ),
        switchTheme: _switchThemeData(
          AppColorScheme.dark.surface,
          AppColorScheme.dark.primary,
        ),
        dialogTheme: _dialogTheme(
          backgroundColor: AppColors.primaryDark,
        ),
      );
}

AppBarTheme _appThemeData(Color iconColor, Color backgroundColor) {
  return AppBarTheme(
    iconTheme: IconThemeData(color: iconColor),
    titleTextStyle: AppTypography.styles(iconColor).largeSemi.copyWith(
          color: iconColor,
        ),
    actionsIconTheme: IconThemeData(color: iconColor),
    foregroundColor: iconColor,
    centerTitle: true,
    backgroundColor: backgroundColor,
  );
}

SwitchThemeData _switchThemeData(Color thumbColor, Color trackColor) {
  return SwitchThemeData(
    thumbColor: WidgetStateProperty.all(thumbColor),
    trackColor: WidgetStateProperty.all(trackColor),
  );
}

OutlinedButtonThemeData _outlinedButtonTheme({required Color primaryColor}) {
  return OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: primaryColor,
      overlayColor: primaryColor,
      side: BorderSide(
        color: primaryColor,
        width: 1,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
    ),
  );
}

TextButtonThemeData _textButtonTheme({required Color primaryColor}) {
  return TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: primaryColor,
    ),
  );
}

BottomNavigationBarThemeData _bottomNavigationBarThemeData(
    Color backgroundColor, Color selectedItemColor) {
  return BottomNavigationBarThemeData(
    backgroundColor: backgroundColor,
    selectedItemColor: selectedItemColor,
  );
}

FilledButtonThemeData _filledButtonThemeData(
    {required Color primaryColor, required Color onPrimaryColor}) {
  return FilledButtonThemeData(
    style: ElevatedButton.styleFrom(
      foregroundColor: onPrimaryColor,
      backgroundColor: primaryColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
    ),
  );
}

ExpansionTileThemeData _expansionTileThemeData({
  required Color backgroundColor,
  required Color iconColor,
  required Color collapsedBorderColor,
  required Color textColor,
}) {
  return ExpansionTileThemeData(
    iconColor: iconColor,
    backgroundColor: backgroundColor,
    textColor: textColor,
    // childrenPadding: const EdgeInsets.all(0),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(4),
      side: BorderSide.none,
    ),
    tilePadding: const EdgeInsets.all(0),
    collapsedShape: Border.symmetric(
      horizontal: BorderSide(
        color: collapsedBorderColor,
        width: 1,
      ),
    ),
  );
}

DialogTheme _dialogTheme({
  required Color backgroundColor,
}) {
  return DialogTheme(
    backgroundColor: backgroundColor,
  );
}
