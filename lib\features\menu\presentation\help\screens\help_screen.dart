import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  final String subject = "BuyerBoard - Help or Feedback";
  final String toEmail = "<EMAIL>";

  final String discordUrl = "https://discord.gg/MUdDuF3V9D";

  // Function to launch Discord invite link
  void launchDiscord(BuildContext context) async {
    final Uri url = Uri.parse(discordUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else if (context.mounted) {
      _showSnackBar(context, 'Could not launch Discord');
    }
  }

  void launchEmailClient(BuildContext context) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: toEmail,
      query: 'subject=$subject',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else if (context.mounted) {
      _showSnackBar(context, 'Could not launch email client');
    }
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: context.appColors.error,
        content: Text(message),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final xColors = context.appColors;
    final typography = context.typography;
    return Scaffold(
      appBar: ApplicationAppBar.buildAppBar(
        context,
        title: Strings.help,
        leadingWidget: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            size: Dimensions.padding_28,
          ),
          // color: AppColors.white,
          onPressed: () => context.shouldPop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(Dimensions.materialPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.contactUs,
              style: typography.large1xBlack,
            ),
            // Text(
            //   Strings.stillNeedHelp,
            //   style: typography.mediumReg,
            // ),
            spacerH8,
            Text(
              Strings.contactUsDescription,
              style: typography.mediumReg.copyWith(
                color: xColors.greyDefaultGreyMedium,
              ),
            ),
            spacerH24,
            _ActionButton(
              label: 'Send us an email',
              icon: Icons.email_outlined,
              onTap: () => launchEmailClient(context),
            ),
            spacerH8,
            _ActionButton(
              label: 'Join Us On Discord',
              icon: Icons.open_in_new,
              isOutlined: true,
              onTap: () => launchDiscord(context),
            ),
            // spacerH24,
            // Text(
            //   Strings.helpfulTopics,
            //   style: typography.large1xBlack,
            // ),
            // Text(
            //   Strings.helpfulTopicsDescription,
            //   style: typography.mediumReg,
            // ),
            // spacerH24,
            // const TopicListWidget(),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  const _ActionButton({
    super.key,
    required this.label,
    required this.icon,
    required this.onTap,
    this.isOutlined = false,
  });

  final String label;
  final IconData icon;
  final VoidCallback onTap;
  final bool isOutlined;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Ink(
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: isOutlined ? Colors.transparent : context.appColors.pPXLight,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: context.appColors.pPXLight,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Text(
              label,
              style: context.typography.mediumSemi.copyWith(
                color: isOutlined
                    ? context.appColors.pPXLight
                    : context.appColors.whitePDark,
              ),
            ),
            const Spacer(),
            Icon(
              icon,
              size: 16,
              color: isOutlined
                  ? context.appColors.pPXLight
                  : context.appColors.whitePDark,
            )
          ],
        ),
      ),
    );
  }
}
