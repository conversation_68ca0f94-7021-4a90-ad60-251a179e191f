import 'package:flutter/material.dart';

abstract final class AppColorScheme {
  static ColorScheme get light => const ColorScheme.light(
        // Primary Colors
        primary: Color(0xff0E8CD3),
        onPrimary: Color(0xffFFFFFF),
        primaryContainer: Color(0xffCAEAFC),
        onPrimaryContainer: Color(0xff0E8CD3),
        inversePrimary: Color(0xff28A7F1),
        primaryFixed: Color(0xff0E8CD3),
        onPrimaryFixed: Color(0xffFFFFFF),

        // Surface Colors
        surface: Color(0xffFFFFFF),
        onSurface: Color(0xff000000),
        onSurfaceVariant: Color(0xff757575),
        surfaceContainer: Color(0xffDBDBDB),
        surfaceContainerHigh: Color(0xffADB3BC),
        surfaceContainerLow: Colors.white,
        inverseSurface: Color(0xff000000),
        onInverseSurface: Color(0xffFFFFFF),

        // Secondary Colors
        secondaryContainer: Color(0xff0E8CD3),
        onSecondaryContainer: Color(0xffFFFFFF),

        // Outline Colors
        outline: Color(0xff212121),
        outlineVariant: Color(0xffDBDBDB),

        // Error Colors
        error: Color(0xffE11B1B),
        onError: Color(0xffFFFFFF),
      );

  static ColorScheme get dark => const ColorScheme.dark(
        // Primary Colors
        primary: Color(0xffCAEAFC),
        onPrimary: Color(0xff063956),
        primaryContainer: Color(0xffCAEAFC),
        onSurfaceVariant: Color(0xff757575),
        onPrimaryContainer: Color(0xff0E8CD3),
        inversePrimary: Color(0xff96D4F8),
        primaryFixed: Color(0xff0E8CD3),
        onPrimaryFixed: Color(0xffFFFFFF),

        // Surface Colors
        surface: Color(0xff0F0F0F),
        onSurface: Color(0xffFFFFFF),

        surfaceContainer: Color(0xff212121),
        surfaceContainerLow: Color(0xff031C2B),
        inverseSurface: Color(0xffBABABA),
        onInverseSurface: Color(0xff000000),

        // Secondary Colors
        secondaryContainer: Color(0xff063956),
        onSecondaryContainer: Color(0xffCAEAFC),

        // Outline Colors
        outline: Color(0xff757575),
        outlineVariant: Color.fromARGB(255, 60, 59, 59),

        // Error Colors
        error: Color(0xffE11B1B),
        onError: Color(0xffFFFFFF),
      );
}
