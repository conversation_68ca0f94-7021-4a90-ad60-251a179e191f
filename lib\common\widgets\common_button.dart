import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/drawables.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

enum ButtonType {
  loginWithEmail,
  loginWithGoogle,
  loginWithApple,
  basic,
  outline
}

// ignore: must_be_immutable
class CommonButton extends StatelessWidget {
  CommonButton({
    super.key,
    this.backgroundColor,
    this.leadingIcon,
    this.trailingIcon,
    this.buttonType = ButtonType.basic,
    this.textColor,
    required this.label,
    required this.action,
    this.borderColor,
  }) : isOutlined = false;
  CommonButton.basic({
    super.key,
    this.backgroundColor,
    this.buttonType = ButtonType.basic,
    this.textColor = AppColors.white,
    required this.label,
    required this.action,
    this.borderColor,
  }) : isOutlined = false;
  CommonButton.loginWithApple({
    super.key,
    required this.action,
    this.textColor,
    this.isOutlined = false,
  })  : label = Strings.loginWithApple,
        buttonType = ButtonType.loginWithApple,
        leadingIcon = SvgPicture.asset(Drawables.icApple,
            colorFilter: textColor != null
                ? ColorFilter.mode(textColor, BlendMode.srcIn)
                : null),
        trailingIcon = SvgPicture.asset(Drawables.icArrowRight,
            colorFilter: textColor != null
                ? ColorFilter.mode(textColor, BlendMode.srcIn)
                : null),
        borderColor = AppColors.white,
        backgroundColor = AppColors.white;

  CommonButton.loginWithGoogle({
    super.key,
    required this.action,
    this.textColor,
    this.isOutlined = false,
  })  : label = Strings.loginWithGoogle,
        buttonType = ButtonType.loginWithGoogle,
        leadingIcon = SvgPicture.asset(
          Drawables.icGoogle,
          colorFilter: textColor != null
              ? ColorFilter.mode(textColor, BlendMode.srcIn)
              : null,
        ),
        trailingIcon = SvgPicture.asset(Drawables.icArrowRight,
            colorFilter: textColor != null
                ? ColorFilter.mode(textColor, BlendMode.srcIn)
                : null),
        borderColor = AppColors.white,
        backgroundColor = AppColors.white;
  CommonButton.loginWithEmailPassword({
    super.key,
    required this.action,
    this.isOutlined = false,
  })  : label = Strings.loginWithEmail,
        buttonType = ButtonType.loginWithEmail,
        textColor = AppColors.white,
        leadingIcon = SvgPicture.asset(Drawables.icMail,
            colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn)),
        trailingIcon = SvgPicture.asset(Drawables.icArrowRight,
            colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn)),
        borderColor = AppColors.black,
        backgroundColor = AppColors.black;
  final ButtonType buttonType;
  final Color? backgroundColor;
  final Color? borderColor;
  Widget? leadingIcon;
  Widget? trailingIcon;
  final String label;
  Color? textColor;
  final VoidCallback action;
  final bool isOutlined;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: ElevatedButton(
        onPressed: action,
        style: ElevatedButton.styleFrom(
          elevation: buttonType == ButtonType.basic ||
                  buttonType == ButtonType.outline ||
                  isOutlined
              ? 0
              : null,
          foregroundColor: textColor ?? context.colorScheme.primary,
          textStyle: context.typography.mediumSemi,
          shadowColor: buttonType == ButtonType.basic ||
                  buttonType == ButtonType.outline ||
                  isOutlined
              ? Colors.transparent
              : null,
          minimumSize: const Size(double.infinity, 44),
          backgroundColor: buttonType == ButtonType.outline || isOutlined
              ? Colors.transparent
              : backgroundColor,
          shape: RoundedRectangleBorder(
            side: BorderSide(
                color: isOutlined
                    ? context.colorScheme.primary
                    : borderColor ?? backgroundColor ?? AppColors.white),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        child: Row(
          children: [
            if (leadingIcon != null) leadingIcon!,
            const Spacer(),
            Text(label),
            const Spacer(),
            if (trailingIcon != null) trailingIcon!,
          ],
        ),
      ),
    );
  }
}
